
# API Service Module (Extended with Auto Scaling)

Este módulo incluye TODO lo necesario para desplegar una API en ECS con Fargate, incluyendo:

- Roles IAM (execution/task roles + permisos logs + permisos EFS opcionales)
- Security Group con puertos restringidos
- CloudWatch log group
- ECS task definition y ECS service
- ALB Target Group y Listener Rule
- DNS específico en Route53
- EFS (Elastic File System) opcional con mount targets y security groups
- **Auto Scaling completo con Application Auto Scaling**
- **Políticas de scaling basadas en CPU y memoria**
- **CloudWatch alarms para monitoreo**

## Uso

### Ejemplo básico (sin EFS)

```hcl
module "api_service" {
  source = "./modules/api-service"
  name   = "account-api"
  image  = "ubuntu"
  cpu    = "256"
  memory = "512"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30
}
```

### Ejemplo con EFS habilitado

```hcl
module "api_service_with_efs" {
  source = "./modules/api-service"
  name   = "report-api"
  image  = "ubuntu"
  cpu    = "512"
  memory = "1024"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # EFS Configuration
  enable_efs = true
  efs_mount_path = "/mnt/shared"
  efs_performance_mode = "generalPurpose"
  efs_throughput_mode = "bursting"

  # Auto Scaling Configuration
  enable_auto_scaling   = true
  min_capacity         = 2
  max_capacity         = 20
  desired_capacity     = 5
  cpu_target_value     = 70
  memory_target_value  = 70
  scale_in_cooldown    = 300
  scale_out_cooldown   = 300
}
```

### Ejemplo con auto scaling personalizado

```hcl
module "api_service_custom_scaling" {
  source = "./modules/api-service"
  name   = "high-traffic-api"
  image  = "my-app:latest"
  cpu    = "1024"
  memory = "2048"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # Auto Scaling Configuration para alta demanda
  enable_auto_scaling   = true
  min_capacity         = 5
  max_capacity         = 50
  desired_capacity     = 10
  cpu_target_value     = 60    # Más agresivo
  memory_target_value  = 60    # Más agresivo
  scale_in_cooldown    = 600   # 10 minutos
  scale_out_cooldown   = 180   # 3 minutos
}
```

### Ejemplo sin auto scaling

```hcl
module "api_service_no_scaling" {
  source = "./modules/api-service"
  name   = "simple-api"
  image  = "nginx"
  cpu    = "256"
  memory = "512"
  application_port = 80
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # Auto Scaling deshabilitado
  enable_auto_scaling = false
  desired_capacity    = 2  # Capacidad fija
}
```

## Variables de Auto Scaling

| Variable | Tipo | Default | Descripción |
|----------|------|---------|-------------|
| `enable_auto_scaling` | bool | `false` | Habilita/deshabilita auto scaling |
| `min_capacity` | number | `1` | Número mínimo de tareas |
| `max_capacity` | number | `10` | Número máximo de tareas |
| `desired_capacity` | number | `1` | Número deseado de tareas inicial |
| `cpu_target_value` | number | `70` | Porcentaje objetivo de CPU para scaling |
| `memory_target_value` | number | `70` | Porcentaje objetivo de memoria para scaling |
| `scale_in_cooldown` | number | `300` | Cooldown en segundos para scale in |
| `scale_out_cooldown` | number | `300` | Cooldown en segundos para scale out |

## Recursos de Auto Scaling Creados

Cuando `enable_auto_scaling = true`, el módulo crea:

1. **Application Auto Scaling Target** (`aws_appautoscaling_target`)
   - Configura el servicio ECS como target escalable
   - Define capacidad mínima y máxima

2. **Políticas de Auto Scaling** (`aws_appautoscaling_policy`)
   - Política basada en CPU utilization
   - Política basada en Memory utilization
   - Ambas usan Target Tracking Scaling

3. **CloudWatch Alarms** (`aws_cloudwatch_metric_alarm`)
   - Alarma para CPU alta (>80%)
   - Alarma para memoria alta (>80%)
   - Monitoreo del servicio ECS

## Métricas y Monitoreo

El auto scaling se basa en las siguientes métricas de CloudWatch:

- **ECSServiceAverageCPUUtilization**: Promedio de CPU del servicio
- **ECSServiceAverageMemoryUtilization**: Promedio de memoria del servicio

Las alarmas adicionales alertan cuando:
- CPU > 80% por 2 períodos consecutivos (10 minutos)
- Memoria > 80% por 2 períodos consecutivos (10 minutos)

## Variables de EFS

- `enable_efs`: (bool, default: false) Habilita la creación de EFS
- `efs_mount_path`: (string, default: "/mnt/efs") Ruta donde se montará EFS en el contenedor
- `efs_performance_mode`: (string, default: "generalPurpose") Modo de rendimiento de EFS
- `efs_throughput_mode`: (string, default: "bursting") Modo de throughput de EFS

## Configuración por API en enterprise_service.tf

Cada API puede tener su propia configuración de autoscaling:

```hcl
locals {
  enterprise_apis = {
    "my-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/health"
      enable_efs       = false

      # Auto Scaling Configuration
      enable_auto_scaling = true
      min_capacity        = 1
      max_capacity        = 10
      desired_capacity    = 2
      cpu_target_value    = 70
      memory_target_value = 70
      scale_in_cooldown   = 300
      scale_out_cooldown  = 300
    }
  }
}
```

### Ejemplos de configuración por tipo de API:

**API de alta demanda (como reportes):**
```hcl
enable_auto_scaling = true
min_capacity        = 2
max_capacity        = 15
desired_capacity    = 3
cpu_target_value    = 60  # Más agresivo
memory_target_value = 60  # Más agresivo
scale_in_cooldown   = 600 # 10 minutos
scale_out_cooldown  = 180 # 3 minutos
```

**API de mantenimiento:**
```hcl
enable_auto_scaling = true
min_capacity        = 1
max_capacity        = 5
desired_capacity    = 1
cpu_target_value    = 80  # Menos agresivo
memory_target_value = 80  # Menos agresivo
```

**API sin autoscaling:**
```hcl
enable_auto_scaling = false
# Solo se usará desired_capacity como capacidad fija
```

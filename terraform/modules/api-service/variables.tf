
variable "name" { type = string }
variable "image" { type = string }
variable "cpu" { type = string }
variable "memory" { type = string }
variable "application_port" { type = number }

variable "vpc_id" { type = string }
variable "subnet_ids" { type = list(string) }
variable "private_cidr_block" { type = string }
variable "health_check_url" { type = string }

variable "cluster_name" { type = string }
variable "alb_listener_arn" { type = string }
variable "envzone_name" { type = string }
variable "envzone_zone_id" { type = string }
variable "alb_dns_name" { type = string }

variable "log_retention_days" { type = number }

# Environment variable for tagging
variable "environment" {
  type        = string
  default     = "dev"
  description = "Environment name for tagging (dev, stg, prd)"
}

# EFS Configuration
variable "enable_efs" {
  type        = bool
  default     = false
  description = "Whether to create and mount an EFS file system"
}

variable "efs_mount_path" {
  type        = string
  default     = "reports"
  description = "Path where EFS will be mounted in the container"
}

variable "efs_performance_mode" {
  type        = string
  default     = "generalPurpose"
  description = "EFS performance mode (generalPurpose or maxIO)"
  validation {
    condition     = contains(["generalPurpose", "maxIO"], var.efs_performance_mode)
    error_message = "EFS performance mode must be either 'generalPurpose' or 'maxIO'."
  }
}

variable "efs_throughput_mode" {
  type        = string
  default     = "bursting"
  description = "EFS throughput mode (bursting or provisioned)"
  validation {
    condition     = contains(["bursting", "provisioned"], var.efs_throughput_mode)
    error_message = "EFS throughput mode must be either 'bursting' or 'provisioned'."
  }
}

# Auto Scaling Configuration
variable "enable_auto_scaling" {
  type        = bool
  default     = false
  description = "Whether to enable auto scaling for the ECS service"
}

variable "min_capacity" {
  type        = number
  default     = 1
  description = "Minimum number of tasks for auto scaling"
  validation {
    condition     = var.min_capacity >= 0
    error_message = "Minimum capacity must be greater than or equal to 0."
  }
}

variable "max_capacity" {
  type        = number
  default     = 10
  description = "Maximum number of tasks for auto scaling"
  validation {
    condition     = var.max_capacity >= 1
    error_message = "Maximum capacity must be greater than or equal to 1."
  }
}

variable "desired_capacity" {
  type        = number
  default     = 1
  description = "Desired number of tasks (initial capacity)"
  validation {
    condition     = var.desired_capacity >= 0
    error_message = "Desired capacity must be greater than or equal to 0."
  }
}

variable "cpu_target_value" {
  type        = number
  default     = 70
  description = "Target CPU utilization percentage for auto scaling"
  validation {
    condition     = var.cpu_target_value > 0 && var.cpu_target_value <= 100
    error_message = "CPU target value must be between 1 and 100."
  }
}

variable "memory_target_value" {
  type        = number
  default     = 70
  description = "Target memory utilization percentage for auto scaling"
  validation {
    condition     = var.memory_target_value > 0 && var.memory_target_value <= 100
    error_message = "Memory target value must be between 1 and 100."
  }
}

variable "scale_in_cooldown" {
  type        = number
  default     = 300
  description = "Cooldown period (in seconds) for scale in operations"
  validation {
    condition     = var.scale_in_cooldown >= 0
    error_message = "Scale in cooldown must be greater than or equal to 0."
  }
}

variable "scale_out_cooldown" {
  type        = number
  default     = 300
  description = "Cooldown period (in seconds) for scale out operations"
  validation {
    condition     = var.scale_out_cooldown >= 0
    error_message = "Scale out cooldown must be greater than or equal to 0."
  }
}

locals {
  enterprise_apis = {
    "enterprise-account-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/account-cl/health"
      enable_efs       = false #true para habilitar EFS
      efs_mount_path   = "/mnt/shared"
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "enterprise-general-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/general-cl/health"
      enable_efs       = false # TRUE para habilitar EFS
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "enterprise-blacklist-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/blacklist-cl/health"
      enable_efs       = false # true para habilitar EFS
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "enterprise-webservice-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/ws-enterprise-cl/health"
      enable_efs       = false # true para habilitar EFS
      # Auto Scaling Configuration - Habilitado para esta API
      enable_auto_scaling = true
      min_capacity        = 1
      max_capacity        = 10
      desired_capacity    = 2
      cpu_target_value    = 70
      memory_target_value = 70
      scale_in_cooldown   = 300
      scale_out_cooldown  = 300
    },
    "enterprise-report-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/report-cl/health"
      enable_efs       = true  # EFS para generar reportes
      efs_mount_path   = "/reports"
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "enterprise-maintenance-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/actuator/health"
      enable_efs       = false # true para habilitar EFS
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "common-shorturl-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/shorturl/actuator/health"
      enable_efs       = false # true para habilitar EFS
      # Auto Scaling Configuration - Deshabilitado para esta API
      enable_auto_scaling = false
    },
    "common-mailer-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 80 # se coloca en 80 debido a que se usa nginx para validar
      health_check_url = "/health"
      enable_efs       = false # true para habilitar EFS
      # Auto Scaling Configuration - Deshabilitado para mailer
      enable_auto_scaling = false
    }
  }
}

module "java_api_services" {
  for_each = local.enterprise_apis
  source   = "./modules/api-service"

  name                = each.key
  image               = each.value.image
  cpu                 = each.value.cpu
  memory              = each.value.memory
  application_port    = each.value.ports
  health_check_url    = each.value.health_check_url

  vpc_id              = local.vpc_id
  subnet_ids          = local.subnet_private_ids

  cluster_name        = local.cluster_name
  alb_listener_arn    = local.albext_https_listener_arn
  envzone_name        = local.envzone_name
  envzone_zone_id     = local.envzone_zone_id
  alb_dns_name        = local.albext_dns_name
  private_cidr_block  = "10.0.0.0/8"
  log_retention_days  = local.retention_in_days

  # EFS Configuration (optional per API)
  enable_efs       = lookup(each.value, "enable_efs", false)
  efs_mount_path   = lookup(each.value, "efs_mount_path", "/reports")

  # Auto Scaling Configuration (optional per API)
  enable_auto_scaling = lookup(each.value, "enable_auto_scaling", false)
  min_capacity        = lookup(each.value, "min_capacity", 1)
  max_capacity        = lookup(each.value, "max_capacity", 10)
  desired_capacity    = lookup(each.value, "desired_capacity", 1)
  cpu_target_value    = lookup(each.value, "cpu_target_value", 70)
  memory_target_value = lookup(each.value, "memory_target_value", 70)
  scale_in_cooldown   = lookup(each.value, "scale_in_cooldown", 300)
  scale_out_cooldown  = lookup(each.value, "scale_out_cooldown", 300)

  # Environment for tagging
  environment = local.env
}


